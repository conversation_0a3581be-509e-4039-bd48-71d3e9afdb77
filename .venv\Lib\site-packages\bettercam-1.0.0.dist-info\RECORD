bettercam-1.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bettercam-1.0.0.dist-info/LICENSE,sha256=T-a67pKLltLPD2ojgnWs_YYYLNrsboFGZU80zwjBybM,1082
bettercam-1.0.0.dist-info/METADATA,sha256=r3nnNnpTJn8hNQyOAPaLvg5jCrpf_vbDFhdbHxPuuHo,7226
bettercam-1.0.0.dist-info/RECORD,,
bettercam-1.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bettercam-1.0.0.dist-info/WHEEL,sha256=Xo9-1PvkuimrydujYJAjF7pCkriuXBpUPEjma1nZyJ0,92
bettercam-1.0.0.dist-info/top_level.txt,sha256=wy384tWfTKrTM35KpbctBgiWZKSzWsSqX9cq_0goLsU,10
bettercam/__init__.py,sha256=pkgqLJvGKyiOncGlB3OEDt9ddL0WnP3mbuGICG2ZvsU,4070
bettercam/__pycache__/__init__.cpython-312.pyc,,
bettercam/__pycache__/bettercam.cpython-312.pyc,,
bettercam/_libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bettercam/_libs/__pycache__/__init__.cpython-312.pyc,,
bettercam/_libs/__pycache__/d3d11.cpython-312.pyc,,
bettercam/_libs/__pycache__/dxgi.cpython-312.pyc,,
bettercam/_libs/__pycache__/user32.cpython-312.pyc,,
bettercam/_libs/d3d11.py,sha256=F3vljLXT43Bts8RJBx_UvwRUsGq5auRuncZ0VFQkWvo,12190
bettercam/_libs/dxgi.py,sha256=ItM2oxDVf_OMavNhgkFWadVPL_Th7cQ_EroO3sIDyVI,7334
bettercam/_libs/user32.py,sha256=ji-xEicWOCffqObQDXvqvD36MI3IKMQDRRcNUCe2kEY,746
bettercam/bettercam.py,sha256=QJ0jcL7OHT5NRrARiBVwmwu6pnVLUqYoGs7ODVvxK7o,9242
bettercam/core/__init__.py,sha256=1I3-ooo_I_D5TL5q0e4kOfKbO4iGinLMmiA1PKlk_l8,251
bettercam/core/__pycache__/__init__.cpython-312.pyc,,
bettercam/core/__pycache__/device.cpython-312.pyc,,
bettercam/core/__pycache__/duplicator.cpython-312.pyc,,
bettercam/core/__pycache__/output.cpython-312.pyc,,
bettercam/core/__pycache__/stagesurf.cpython-312.pyc,,
bettercam/core/device.py,sha256=xxelTrsmtg7J0DHPdbejd5PQNr_HzODfaWq69xW5SWI,2532
bettercam/core/duplicator.py,sha256=1Tv4aPIDzPbzA1i_lSpOXhQhdYcm2OWpwJ0OQmqTITU,1942
bettercam/core/output.py,sha256=tvQEhgZLQgxyWDWjDsI74BRsrr4-HNFI0Ty7cpSOQtk,1706
bettercam/core/stagesurf.py,sha256=_YAeiM9gNb2SafXzVQaihCWVsKKRjq4YhJnTPWepRAA,2359
bettercam/processor/__init__.py,sha256=h9-VFeztCvD8hQm75J3C6SD0SIvMnblJikKzv2CCbeQ,29
bettercam/processor/__pycache__/__init__.cpython-312.pyc,,
bettercam/processor/__pycache__/base.cpython-312.pyc,,
bettercam/processor/__pycache__/cupy_processor.cpython-312.pyc,,
bettercam/processor/__pycache__/numpy_processor.cpython-312.pyc,,
bettercam/processor/base.py,sha256=RSP5UK3uUdOZqnEl_WCh9231VEIRirc8c9t-8Ayb86g,1036
bettercam/processor/cupy_processor.py,sha256=WIlxZbky-U6rY3pHfrMVoASh6pS7PBwp0CK6VPmV22w,2756
bettercam/processor/numpy_processor.py,sha256=0_ZtwUPfruAi7NW2iiw1KlPNbkOKWhprFbhkbyivetU,3018
bettercam/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bettercam/util/__pycache__/__init__.cpython-312.pyc,,
bettercam/util/__pycache__/io.cpython-312.pyc,,
bettercam/util/__pycache__/timer.cpython-312.pyc,,
bettercam/util/io.py,sha256=4pWpjakc8kMEAmJIfGu5eEF_ObfVpz3g4w0oGSpKRVk,3156
bettercam/util/timer.py,sha256=Zjjt-_c6mr1IxrVKPBUweac7fYJgqMprrC8IoLfmUjQ,891
